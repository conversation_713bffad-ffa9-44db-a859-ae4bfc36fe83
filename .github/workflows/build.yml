name: Build

on:
  push:
    branches:
      - master


jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: [ '1.23.4' ]
    permissions: read-all
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Setup Go 1.23.4
        uses: actions/setup-go@v4
        with:
          # Semantic version range syntax or exact version of Go
          go-version: '1.23.4'
      - name: Test with the Go CLI
        run: go test --timeout 10m -coverprofile=coverage.out ./...
      - uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
     
      # If you wish to fail your job when the Quality Gate is red, uncomment the
      # following lines. This would typically be used to fail a deployment.
      # - uses: sonarsource/sonarqube-quality-gate-action@master
      #   timeout-minutes: 5
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
