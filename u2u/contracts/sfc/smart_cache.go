package sfc

import (
	"fmt"
	"math/big"
	"sync"
	"sync/atomic"
	"time"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/state"
	"github.com/unicornultrafoundation/go-u2u/log"
)

// SmartCacheEntry represents a cached value with metadata
type SmartCacheEntry struct {
	Value       common.Hash
	Timestamp   time.Time
	AccessCount uint64
}

// ValidatorInfo represents cached validator information
type ValidatorInfo struct {
	Status           uint64
	DeactivatedTime  uint64
	DeactivatedEpoch uint64
	ReceivedStake    *big.Int
	CreatedEpoch     uint64
	CreatedTime      uint64
	Auth             common.Address
}

// DelegationInfo represents cached delegation information
type DelegationInfo struct {
	Amount          *big.Int
	Epoch           uint64
	ToStakerID      uint64
	LockedStake     *big.Int
	LockupFromEpoch uint64
	LockupEndTime   uint64
	LockupDuration  uint64
}

// EpochData represents cached epoch information
type EpochData struct {
	EndTime               uint64
	EpochFee              *big.Int
	TotalBaseRewardWeight *big.Int
	TotalTxRewardWeight   *big.Int
	BaseRewardPerSecond   *big.Int
	TotalStake            *big.Int
	TotalSupply           *big.Int
	ValidatorIDs          []*big.Int
}

// SmartSfcCache implements intelligent caching for SFC operations
type SmartSfcCache struct {
	*state.StateDB

	// Cache configuration
	enabled      bool
	currentEpoch uint64
	maxCacheSize int
	ttl          time.Duration

	// Specialized caches for high-frequency operations
	validatorCache  sync.Map // map[uint64]*ValidatorInfo
	delegationCache sync.Map // map[string]*DelegationInfo
	epochDataCache  sync.Map // map[uint64]*EpochData
	stateCache      sync.Map // map[string]*SmartCacheEntry

	// Lightweight statistics
	hits       uint64
	misses     uint64
	operations uint64

	// Cache management
	mutex       sync.RWMutex
	lastCleanup time.Time
}

// NewSmartSfcCache creates a new smart SFC cache
func NewSmartSfcCache(stateDB *state.StateDB) *SmartSfcCache {
	return &SmartSfcCache{
		StateDB:      stateDB,
		enabled:      false,
		maxCacheSize: 1000,            // Smaller cache size for better performance
		ttl:          5 * time.Minute, // 5 minute TTL
		lastCleanup:  time.Now(),
	}
}

// EnableForEpoch enables caching for a specific epoch
func (s *SmartSfcCache) EnableForEpoch(epoch uint64) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.enabled = true
	s.currentEpoch = epoch

	// Clear old caches when switching epochs
	s.validatorCache = sync.Map{}
	s.delegationCache = sync.Map{}
	s.epochDataCache = sync.Map{}
	s.stateCache = sync.Map{}

	// Reset statistics
	atomic.StoreUint64(&s.hits, 0)
	atomic.StoreUint64(&s.misses, 0)
	atomic.StoreUint64(&s.operations, 0)

	log.Info("Smart SFC cache enabled", "epoch", epoch, "maxSize", s.maxCacheSize)
}

// Disable disables caching and clears all caches
func (s *SmartSfcCache) Disable() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.enabled {
		return
	}

	s.enabled = false

	// Log final statistics
	hits := atomic.LoadUint64(&s.hits)
	misses := atomic.LoadUint64(&s.misses)
	operations := atomic.LoadUint64(&s.operations)

	var hitRate float64
	if operations > 0 {
		hitRate = float64(hits) / float64(operations) * 100
	}

	log.Info("Smart SFC cache disabled",
		"epoch", s.currentEpoch,
		"operations", operations,
		"hits", hits,
		"misses", misses,
		"hitRate", hitRate)

	// Clear all caches
	s.validatorCache = sync.Map{}
	s.delegationCache = sync.Map{}
	s.epochDataCache = sync.Map{}
	s.stateCache = sync.Map{}
}

// GetState implements enhanced GetState with smart caching
func (s *SmartSfcCache) GetState(addr common.Address, slot common.Hash) common.Hash {
	// Fast path: if cache is disabled, use direct StateDB
	if !s.enabled {
		return s.StateDB.GetState(addr, slot)
	}

	// Only cache SFC contract state
	if addr != ContractAddress {
		return s.StateDB.GetState(addr, slot)
	}

	atomic.AddUint64(&s.operations, 1)

	// Create cache key
	key := fmt.Sprintf("%s_%s", addr.Hex(), slot.Hex())

	// Check cache
	if cached, ok := s.stateCache.Load(key); ok {
		entry := cached.(*SmartCacheEntry)

		// Check TTL
		if time.Since(entry.Timestamp) < s.ttl {
			atomic.AddUint64(&entry.AccessCount, 1)
			atomic.AddUint64(&s.hits, 1)
			return entry.Value
		}

		// TTL expired, remove from cache
		s.stateCache.Delete(key)
	}

	// Cache miss - get from state
	value := s.StateDB.GetState(addr, slot)
	atomic.AddUint64(&s.misses, 1)

	// Store in cache
	entry := &SmartCacheEntry{
		Value:       value,
		Timestamp:   time.Now(),
		AccessCount: 1,
	}
	s.stateCache.Store(key, entry)

	// Periodic cleanup
	s.periodicCleanup()

	return value
}

// SetState implements enhanced SetState with cache invalidation
func (s *SmartSfcCache) SetState(addr common.Address, slot common.Hash, value common.Hash) {
	// Always write to underlying StateDB
	s.StateDB.SetState(addr, slot, value)

	// If cache is disabled, nothing more to do
	if !s.enabled {
		return
	}

	// Only manage cache for SFC contract
	if addr != ContractAddress {
		return
	}

	// Invalidate cache entry
	key := fmt.Sprintf("%s_%s", addr.Hex(), slot.Hex())
	s.stateCache.Delete(key)

	// Invalidate related cached data based on slot
	s.invalidateRelatedCache(slot)
}

// invalidateRelatedCache invalidates cached data related to a storage slot
func (s *SmartSfcCache) invalidateRelatedCache(slot common.Hash) {
	// Convert slot to big.Int for comparison
	slotBig := new(big.Int).SetBytes(slot.Bytes())

	// Invalidate validator cache if validator-related slot changed
	if s.isValidatorSlot(slotBig) {
		validatorID := s.extractValidatorID(slotBig)
		if validatorID != nil {
			s.validatorCache.Delete(validatorID.Uint64())
		}
	}

	// Invalidate delegation cache if delegation-related slot changed
	if s.isDelegationSlot(slotBig) {
		// For delegation slots, we'd need to clear related entries
		// This is a simplified approach - in practice, you might want more granular invalidation
	}

	// Invalidate epoch cache if epoch-related slot changed
	if s.isEpochSlot(slotBig) {
		epochID := s.extractEpochID(slotBig)
		if epochID != nil {
			s.epochDataCache.Delete(epochID.Uint64())
		}
	}
}

// periodicCleanup performs periodic cache cleanup
func (s *SmartSfcCache) periodicCleanup() {
	now := time.Now()
	if now.Sub(s.lastCleanup) < time.Minute {
		return // Only cleanup once per minute
	}

	s.lastCleanup = now

	// Cleanup expired entries
	s.stateCache.Range(func(key, value interface{}) bool {
		entry := value.(*SmartCacheEntry)
		if now.Sub(entry.Timestamp) > s.ttl {
			s.stateCache.Delete(key)
		}
		return true
	})
}

// Helper methods for slot identification
func (s *SmartSfcCache) isValidatorSlot(slot *big.Int) bool {
	// Check if slot is in validator-related range
	// This is a simplified check - you'd implement proper slot range checking
	return slot.Cmp(big.NewInt(1000)) > 0 && slot.Cmp(big.NewInt(10000)) < 0
}

func (s *SmartSfcCache) isDelegationSlot(slot *big.Int) bool {
	// Check if slot is in delegation-related range
	return slot.Cmp(big.NewInt(10000)) > 0 && slot.Cmp(big.NewInt(50000)) < 0
}

func (s *SmartSfcCache) isEpochSlot(slot *big.Int) bool {
	// Check if slot is in epoch-related range
	return slot.Cmp(big.NewInt(50000)) > 0
}

func (s *SmartSfcCache) extractValidatorID(slot *big.Int) *big.Int {
	// Extract validator ID from slot - simplified implementation
	// You'd implement proper slot parsing based on your storage layout
	return big.NewInt(1) // Placeholder
}

func (s *SmartSfcCache) extractEpochID(slot *big.Int) *big.Int {
	// Extract epoch ID from slot - simplified implementation
	return big.NewInt(1) // Placeholder
}

// GetCacheStats returns current cache statistics
func (s *SmartSfcCache) GetCacheStats() (hits, misses, operations uint64, hitRate float64) {
	hits = atomic.LoadUint64(&s.hits)
	misses = atomic.LoadUint64(&s.misses)
	operations = atomic.LoadUint64(&s.operations)

	if operations > 0 {
		hitRate = float64(hits) / float64(operations) * 100
	}

	return hits, misses, operations, hitRate
}

// High-level caching methods for specific SFC operations

// GetValidatorInfo returns cached validator information
func (s *SmartSfcCache) GetValidatorInfo(validatorID uint64) *ValidatorInfo {
	if !s.enabled {
		return s.loadValidatorInfo(validatorID)
	}

	// Check cache first
	if cached, ok := s.validatorCache.Load(validatorID); ok {
		atomic.AddUint64(&s.hits, 1)
		return cached.(*ValidatorInfo)
	}

	// Load from state
	info := s.loadValidatorInfo(validatorID)
	atomic.AddUint64(&s.misses, 1)

	// Cache the result
	s.validatorCache.Store(validatorID, info)

	return info
}

// loadValidatorInfo loads validator information from state
func (s *SmartSfcCache) loadValidatorInfo(validatorID uint64) *ValidatorInfo {
	validatorIDBig := big.NewInt(int64(validatorID))

	// Get validator status slot
	statusSlot, _ := getValidatorStatusSlot(validatorIDBig)
	status := s.StateDB.GetState(ContractAddress, common.BigToHash(statusSlot))

	// Get validator auth slot
	authSlot, _ := getValidatorAuthSlot(validatorIDBig)
	auth := s.StateDB.GetState(ContractAddress, common.BigToHash(authSlot))

	// Get received stake slot
	receivedStakeSlot, _ := getValidatorReceivedStakeSlot(validatorIDBig)
	receivedStake := s.StateDB.GetState(ContractAddress, common.BigToHash(receivedStakeSlot))

	return &ValidatorInfo{
		Status:        new(big.Int).SetBytes(status.Bytes()).Uint64(),
		ReceivedStake: new(big.Int).SetBytes(receivedStake.Bytes()),
		Auth:          common.BytesToAddress(auth.Bytes()),
	}
}

// GetDelegationInfo returns cached delegation information
func (s *SmartSfcCache) GetDelegationInfo(delegator common.Address, validatorID uint64) *DelegationInfo {
	if !s.enabled {
		return s.loadDelegationInfo(delegator, validatorID)
	}

	key := fmt.Sprintf("%s_%d", delegator.Hex(), validatorID)

	// Check cache first
	if cached, ok := s.delegationCache.Load(key); ok {
		atomic.AddUint64(&s.hits, 1)
		return cached.(*DelegationInfo)
	}

	// Load from state
	info := s.loadDelegationInfo(delegator, validatorID)
	atomic.AddUint64(&s.misses, 1)

	// Cache the result
	s.delegationCache.Store(key, info)

	return info
}

// loadDelegationInfo loads delegation information from state
func (s *SmartSfcCache) loadDelegationInfo(delegator common.Address, validatorID uint64) *DelegationInfo {
	validatorIDBig := big.NewInt(int64(validatorID))

	// Get stake slot
	stakeSlot, _ := getStakeSlot(delegator, validatorIDBig)
	stake := s.StateDB.GetState(ContractAddress, common.BigToHash(stakeSlot))

	// Get locked stake slot
	lockedStakeSlot, _ := getLockedStakeSlot(delegator, validatorIDBig)
	lockedStake := s.StateDB.GetState(ContractAddress, common.BigToHash(lockedStakeSlot))

	// Get lockup duration slot
	lockupDurationSlot, _ := getLockupDurationSlot(delegator, validatorIDBig)
	lockupDuration := s.StateDB.GetState(ContractAddress, common.BigToHash(lockupDurationSlot))

	return &DelegationInfo{
		Amount:         new(big.Int).SetBytes(stake.Bytes()),
		ToStakerID:     validatorID,
		LockedStake:    new(big.Int).SetBytes(lockedStake.Bytes()),
		LockupDuration: new(big.Int).SetBytes(lockupDuration.Bytes()).Uint64(),
	}
}

// GetEpochData returns cached epoch information
func (s *SmartSfcCache) GetEpochData(epoch uint64) *EpochData {
	if !s.enabled {
		return s.loadEpochData(epoch)
	}

	// Check cache first
	if cached, ok := s.epochDataCache.Load(epoch); ok {
		atomic.AddUint64(&s.hits, 1)
		return cached.(*EpochData)
	}

	// Load from state
	data := s.loadEpochData(epoch)
	atomic.AddUint64(&s.misses, 1)

	// Cache the result
	s.epochDataCache.Store(epoch, data)

	return data
}

// loadEpochData loads epoch information from state
func (s *SmartSfcCache) loadEpochData(epoch uint64) *EpochData {
	epochBig := big.NewInt(int64(epoch))

	// Get epoch snapshot slot
	snapshotSlot, _ := getEpochSnapshotSlot(epochBig)

	// Get end time
	endTimeSlot := new(big.Int).Add(snapshotSlot, big.NewInt(int64(endTimeOffset)))
	endTime := s.StateDB.GetState(ContractAddress, common.BigToHash(endTimeSlot))

	// Get total stake
	totalStakeSlot := new(big.Int).Add(snapshotSlot, big.NewInt(int64(totalStakeOffset)))
	totalStake := s.StateDB.GetState(ContractAddress, common.BigToHash(totalStakeSlot))

	// Get base reward per second
	baseRewardSlot := new(big.Int).Add(snapshotSlot, big.NewInt(int64(baseRewardPerSecondOffset)))
	baseReward := s.StateDB.GetState(ContractAddress, common.BigToHash(baseRewardSlot))

	return &EpochData{
		EndTime:             new(big.Int).SetBytes(endTime.Bytes()).Uint64(),
		TotalStake:          new(big.Int).SetBytes(totalStake.Bytes()),
		BaseRewardPerSecond: new(big.Int).SetBytes(baseReward.Bytes()),
	}
}

// InvalidateValidator invalidates cached validator data
func (s *SmartSfcCache) InvalidateValidator(validatorID uint64) {
	s.validatorCache.Delete(validatorID)
}

// InvalidateDelegation invalidates cached delegation data
func (s *SmartSfcCache) InvalidateDelegation(delegator common.Address, validatorID uint64) {
	key := fmt.Sprintf("%s_%d", delegator.Hex(), validatorID)
	s.delegationCache.Delete(key)
}

// InvalidateEpoch invalidates cached epoch data
func (s *SmartSfcCache) InvalidateEpoch(epoch uint64) {
	s.epochDataCache.Delete(epoch)
}

// Global smart cache management

var (
	globalSmartCache *SmartSfcCache
	smartCacheMutex  sync.RWMutex
)

// EnableSmartCaching enables smart caching for specific SFC operations
func EnableSmartCaching(stateDB *state.StateDB, epoch uint64) *SmartSfcCache {
	smartCacheMutex.Lock()
	defer smartCacheMutex.Unlock()

	// Create or reuse smart cache
	if globalSmartCache == nil || globalSmartCache.StateDB != stateDB {
		globalSmartCache = NewSmartSfcCache(stateDB)
	}

	globalSmartCache.EnableForEpoch(epoch)
	return globalSmartCache
}

// DisableSmartCaching disables smart caching
func DisableSmartCaching() {
	smartCacheMutex.RLock()
	defer smartCacheMutex.RUnlock()

	if globalSmartCache != nil {
		globalSmartCache.Disable()
	}
}

// GetSmartCache returns the global smart cache instance
func GetSmartCache() *SmartSfcCache {
	smartCacheMutex.RLock()
	defer smartCacheMutex.RUnlock()

	return globalSmartCache
}

// WithSmartCache executes a function with smart caching enabled
func WithSmartCache(stateDB *state.StateDB, epoch uint64, fn func(*SmartSfcCache) error) error {
	cache := EnableSmartCaching(stateDB, epoch)
	defer cache.Disable()

	return fn(cache)
}

// Context-aware caching functions for specific SFC operations

// CachedGetValidator returns validator information with caching
func CachedGetValidator(stateDB *state.StateDB, validatorID uint64) *ValidatorInfo {
	smartCacheMutex.RLock()
	cache := globalSmartCache
	smartCacheMutex.RUnlock()

	if cache != nil && cache.enabled {
		return cache.GetValidatorInfo(validatorID)
	}

	// Fallback to direct state access
	tempCache := NewSmartSfcCache(stateDB)
	return tempCache.loadValidatorInfo(validatorID)
}

// CachedGetDelegation returns delegation information with caching
func CachedGetDelegation(stateDB *state.StateDB, delegator common.Address, validatorID uint64) *DelegationInfo {
	smartCacheMutex.RLock()
	cache := globalSmartCache
	smartCacheMutex.RUnlock()

	if cache != nil && cache.enabled {
		return cache.GetDelegationInfo(delegator, validatorID)
	}

	// Fallback to direct state access
	tempCache := NewSmartSfcCache(stateDB)
	return tempCache.loadDelegationInfo(delegator, validatorID)
}

// CachedGetEpochData returns epoch information with caching
func CachedGetEpochData(stateDB *state.StateDB, epoch uint64) *EpochData {
	smartCacheMutex.RLock()
	cache := globalSmartCache
	smartCacheMutex.RUnlock()

	if cache != nil && cache.enabled {
		return cache.GetEpochData(epoch)
	}

	// Fallback to direct state access
	tempCache := NewSmartSfcCache(stateDB)
	return tempCache.loadEpochData(epoch)
}

// Smart caching for specific getter functions

// SmartGetValidatorStatus returns validator status with smart caching
func SmartGetValidatorStatus(stateDB *state.StateDB, validatorID uint64) uint64 {
	info := CachedGetValidator(stateDB, validatorID)
	return info.Status
}

// SmartGetValidatorAuth returns validator auth address with smart caching
func SmartGetValidatorAuth(stateDB *state.StateDB, validatorID uint64) common.Address {
	info := CachedGetValidator(stateDB, validatorID)
	return info.Auth
}

// SmartGetValidatorReceivedStake returns validator received stake with smart caching
func SmartGetValidatorReceivedStake(stateDB *state.StateDB, validatorID uint64) *big.Int {
	info := CachedGetValidator(stateDB, validatorID)
	return info.ReceivedStake
}

// SmartGetDelegationAmount returns delegation amount with smart caching
func SmartGetDelegationAmount(stateDB *state.StateDB, delegator common.Address, validatorID uint64) *big.Int {
	info := CachedGetDelegation(stateDB, delegator, validatorID)
	return info.Amount
}

// SmartGetLockedStake returns locked stake with smart caching
func SmartGetLockedStake(stateDB *state.StateDB, delegator common.Address, validatorID uint64) *big.Int {
	info := CachedGetDelegation(stateDB, delegator, validatorID)
	return info.LockedStake
}

// SmartGetEpochEndTime returns epoch end time with smart caching
func SmartGetEpochEndTime(stateDB *state.StateDB, epoch uint64) uint64 {
	data := CachedGetEpochData(stateDB, epoch)
	return data.EndTime
}

// SmartGetEpochTotalStake returns epoch total stake with smart caching
func SmartGetEpochTotalStake(stateDB *state.StateDB, epoch uint64) *big.Int {
	data := CachedGetEpochData(stateDB, epoch)
	return data.TotalStake
}
