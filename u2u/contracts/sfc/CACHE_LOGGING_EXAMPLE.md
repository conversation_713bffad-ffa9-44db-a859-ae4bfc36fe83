# StateDB Cache Logging Examples

This document shows examples of the comprehensive logging output you'll see when the StateDB cache is active during epoch sealing operations.

## Cache Activation Logging

When epoch sealing starts, you'll see:

```
INFO [timestamp] === EPOCH CACHE ACTIVATED ===    epoch=150 maxCacheSize=10000 cacheReady=true
```

## Periodic Progress Updates

Every 1000 operations, the cache logs progress updates:

```
INFO [timestamp] Cache Progress Update             epoch=150 totalOps=1000 cacheEntries=234 readHitRate=45.67 writeHitRate=78.90 overallHitRate=62.30
INFO [timestamp] Cache Progress Update             epoch=150 totalOps=2000 cacheEntries=456 readHitRate=52.34 writeHitRate=81.25 overallHitRate=66.80
INFO [timestamp] Cache Progress Update             epoch=150 totalOps=3000 cacheEntries=678 readHitRate=58.91 writeHitRate=83.47 overallHitRate=71.19
```

## Final Cache Statistics (End of Epoch)

When epoch sealing completes, you'll see comprehensive statistics:

### 1. Manager-Level Summary

```
INFO [timestamp] === EPOCH CACHE FINAL SUMMARY === epoch=150 totalOperations=4567 overallHitRate=68.45

INFO [timestamp] Cache Performance Breakdown       readHits=1234 readMisses=567 readHitRate=68.52 writeHits=890 writeMisses=123 writeHitRate=87.86

INFO [timestamp] Gas Savings Estimate              readGasSaved=2591400 writeGasSaved=17800000 totalGasSaved=20391400 avgGasSavedPerOp=4466.32
```

### 2. Detailed Cache Flush Statistics

```
INFO [timestamp] === EPOCH CACHE STATISTICS ===   epoch=150 totalOperations=4567 cacheEntries=892 dirtyEntries=456

INFO [timestamp] Read Operations                   readHits=1234 readMisses=567 readHitRate=68.52

INFO [timestamp] Write Operations                  writeHits=890 writeMisses=123 writeHitRate=87.86

INFO [timestamp] Overall Performance               overallHitRate=68.45 memoryEfficiency=19.54
```

### 3. Per-Contract Cache Statistics

```
INFO [timestamp] Contract Cache Stats              contract=0x1234...5678 cacheEntries=456 dirtyEntries=234 totalReads=1890 avgReadsPerEntry=4.14

INFO [timestamp] Contract Cache Stats              contract=0xabcd...ef01 cacheEntries=234 dirtyEntries=123 totalReads=945 avgReadsPerEntry=4.04

INFO [timestamp] Contract Cache Stats              contract=0x9876...5432 cacheEntries=202 dirtyEntries=99 totalReads=678 avgReadsPerEntry=3.36
```

### 4. Final Completion Message

```
INFO [timestamp] StateDB cache flushed to StateDB - Epoch Complete

INFO [timestamp] Epoch caching stopped successfully epoch=150
```

## Real-World Example (200 Validators)

Here's what you might see for a medium-sized network with 200 validators:

```
INFO [12:34:56.789] === EPOCH CACHE ACTIVATED ===    epoch=1250 maxCacheSize=10000 cacheReady=true

INFO [12:34:57.123] Cache Progress Update             epoch=1250 totalOps=1000 cacheEntries=287 readHitRate=23.45 writeHitRate=0.00 overallHitRate=23.45

INFO [12:34:57.456] Cache Progress Update             epoch=1250 totalOps=2000 cacheEntries=534 readHitRate=41.23 writeHitRate=67.89 overallHitRate=54.56

INFO [12:34:57.789] Cache Progress Update             epoch=1250 totalOps=3000 cacheEntries=723 readHitRate=52.67 writeHitRate=78.34 overallHitRate=65.51

INFO [12:34:58.012] Cache Progress Update             epoch=1250 totalOps=4000 cacheEntries=891 readHitRate=58.92 writeHitRate=82.15 overallHitRate=70.54

INFO [12:34:58.234] === EPOCH CACHE FINAL SUMMARY === epoch=1250 totalOperations=4523 overallHitRate=71.23

INFO [12:34:58.235] Cache Performance Breakdown       readHits=1456 readMisses=678 readHitRate=68.23 writeHits=1234 writeMisses=267 writeHitRate=82.21

INFO [12:34:58.236] Gas Savings Estimate              readGasSaved=3057600 writeGasSaved=24680000 totalGasSaved=27737600 avgGasSavedPerOp=6132.45

INFO [12:34:58.237] === EPOCH CACHE STATISTICS ===   epoch=1250 totalOperations=4523 cacheEntries=1023 dirtyEntries=567

INFO [12:34:58.238] Read Operations                   readHits=1456 readMisses=678 readHitRate=68.23

INFO [12:34:58.239] Write Operations                  writeHits=1234 writeMisses=267 writeHitRate=82.21

INFO [12:34:58.240] Overall Performance               overallHitRate=71.23 memoryEfficiency=22.61

INFO [12:34:58.241] Contract Cache Stats              contract=0xfc00face00000000000000000000000000000000 cacheEntries=567 dirtyEntries=234 totalReads=2345 avgReadsPerEntry=4.14

INFO [12:34:58.242] Contract Cache Stats              contract=0x239fa7623354ec26520de878b52f13fe84b06971 cacheEntries=234 dirtyEntries=123 totalReads=890 avgReadsPerEntry=3.80

INFO [12:34:58.243] Contract Cache Stats              contract=0x5c7a4290f6f8ff64c69eeffdfafc8644a4ec3a4e cacheEntries=222 dirtyEntries=210 totalReads=567 avgReadsPerEntry=2.55

INFO [12:34:58.244] StateDB cache flushed to StateDB - Epoch Complete

INFO [12:34:58.245] Epoch caching stopped successfully epoch=1250
```

## Performance Metrics Explanation

### Hit Rates
- **Read Hit Rate**: Percentage of `GetState` operations served from cache
- **Write Hit Rate**: Percentage of `SetState` operations that updated existing cache entries
- **Overall Hit Rate**: Combined percentage of all operations that benefited from caching

### Gas Savings
- **Read Gas Saved**: `readHits × 2100` (SLOAD gas cost)
- **Write Gas Saved**: `writeHits × 20000` (SSTORE gas cost)
- **Total Gas Saved**: Combined gas savings from cache hits
- **Avg Gas Saved Per Op**: Average gas saved per operation

### Memory Efficiency
- **Cache Entries**: Number of unique storage slots cached
- **Dirty Entries**: Number of cached entries that need to be written to StateDB
- **Memory Efficiency**: Ratio of cache entries to total operations (lower is better)
- **Avg Reads Per Entry**: How many times each cached entry was read (higher is better)

## Debug Logging

If debug logging is enabled (`log.SetLevel(log.LvlDebug)`), you'll also see individual cache operations:

```
DEBUG [timestamp] StateDB cache hit                 addr=0xfc00...0000 slot=0x0000...0001 value=0x0000...0042
DEBUG [timestamp] StateDB cache miss                addr=0xfc00...0000 slot=0x0000...0002 value=0x0000...0123
DEBUG [timestamp] StateDB cache write hit           addr=0xfc00...0000 slot=0x0000...0001 value=0x0000...0456
DEBUG [timestamp] StateDB cache flush               addr=0xfc00...0000 slot=0x0000...0001 value=0x0000...0456
DEBUG [timestamp] StateDB cache eviction            addr=0xfc00...0000 slot=0x0000...0003
```

## Monitoring and Alerting

You can monitor cache performance by watching for:

### Good Performance Indicators
- Overall hit rate > 60%
- Read hit rate > 50%
- Write hit rate > 80%
- Memory efficiency < 30%

### Performance Issues
- Overall hit rate < 40% (may indicate cache size too small)
- Memory efficiency > 50% (cache not being reused effectively)
- Frequent eviction messages (cache size too small)

### Example Monitoring Query (if using structured logging)
```
# Prometheus-style query for cache hit rate
avg(cache_overall_hit_rate) by (epoch)

# Alert if hit rate drops below 50%
cache_overall_hit_rate < 50
```

## Configuration Tuning

Based on the logging output, you can tune cache performance:

### If Hit Rate is Low
- Increase `maxCacheSize` in `NewStateDBCache()`
- Check if operations are too diverse (many unique slots)

### If Memory Usage is High
- Decrease `maxCacheSize`
- Reduce `logInterval` for more frequent monitoring

### If Evictions are Frequent
- Increase `maxCacheSize`
- Consider implementing more sophisticated eviction policies

## Integration with Monitoring Systems

The structured logging format makes it easy to integrate with monitoring systems:

```go
// Example: Export metrics to Prometheus
func exportCacheMetrics(readHits, readMisses, writeHits, writeMisses uint64) {
    cacheHitRateGauge.Set(float64(readHits+writeHits) / float64(readHits+readMisses+writeHits+writeMisses))
    cacheOperationsCounter.Add(float64(readHits + readMisses + writeHits + writeMisses))
    gasSavedCounter.Add(float64(readHits*2100 + writeHits*20000))
}
```

This comprehensive logging provides full visibility into cache performance and helps optimize blockchain operations for maximum efficiency.
