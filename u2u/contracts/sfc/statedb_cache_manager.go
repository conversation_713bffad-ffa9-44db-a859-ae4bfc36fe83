package sfc

import (
	"sync"

	"github.com/unicornultrafoundation/go-u2u/core/vm"
	"github.com/unicornultrafoundation/go-u2u/log"
)

// Global cache manager instance
var (
	cacheManager     *StateDBCacheManager
	cacheManagerOnce sync.Once
)

// StateDBCacheManager manages the lifecycle of StateDB caching
type StateDBCacheManager struct {
	cache  *StateDBCache
	mutex  sync.RWMutex
	active bool
}

// GetCacheManager returns the singleton cache manager instance
func GetCacheManager() *StateDBCacheManager {
	cacheManagerOnce.Do(func() {
		cacheManager = &StateDBCacheManager{
			active: false,
		}
	})
	return cacheManager
}

// InitializeCache initializes the cache with the given StateDB
func (cm *StateDBCacheManager) InitializeCache(stateDB vm.StateDB) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	cm.cache = NewStateDBCache(stateDB)
	log.Info("StateDB cache manager initialized")
}

// StartEpochCaching starts caching for the given epoch
func (cm *StateDBCacheManager) StartEpochCaching(epoch uint64) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	if cm.cache == nil {
		log.Warn("StateDB cache not initialized, cannot start epoch caching")
		return
	}
	
	cm.cache.StartEpochCaching(epoch)
	cm.active = true
	
	log.Info("Epoch caching started", "epoch", epoch)
}

// GetCachedStateDB returns the cached StateDB if active, otherwise returns the original StateDB
func (cm *StateDBCacheManager) GetCachedStateDB(originalStateDB vm.StateDB) vm.StateDB {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	if cm.active && cm.cache != nil && cm.cache.IsActive() {
		return cm.cache
	}
	
	return originalStateDB
}

// FlushAndStopCaching flushes all cached data to StateDB and stops caching
func (cm *StateDBCacheManager) FlushAndStopCaching() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	if cm.cache != nil && cm.active {
		cm.cache.StopEpochCaching()
		cm.active = false
		
		// Log final statistics
		readHits, readMisses, writeHits, writeMisses, hitRate := cm.cache.GetCacheStats()
		log.Info("Epoch caching stopped",
			"readHits", readHits,
			"readMisses", readMisses,
			"writeHits", writeHits,
			"writeMisses", writeMisses,
			"hitRate", hitRate)
	}
}

// IsActive returns whether caching is currently active
func (cm *StateDBCacheManager) IsActive() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.active
}

// GetCacheStats returns current cache statistics
func (cm *StateDBCacheManager) GetCacheStats() (readHits, readMisses, writeHits, writeMisses uint64, hitRate float64) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	if cm.cache != nil {
		return cm.cache.GetCacheStats()
	}
	
	return 0, 0, 0, 0, 0
}

// Helper functions for easy integration with existing SFC code

// WrapStateDBWithCache wraps the StateDB with caching if active
func WrapStateDBWithCache(evm *vm.EVM) vm.StateDB {
	cacheManager := GetCacheManager()
	return cacheManager.GetCachedStateDB(evm.StateDB)
}

// StartEpochCachingIfNeeded starts epoch caching if not already active
func StartEpochCachingIfNeeded(evm *vm.EVM, epoch uint64) {
	cacheManager := GetCacheManager()
	
	// Initialize cache if not done yet
	if cacheManager.cache == nil {
		cacheManager.InitializeCache(evm.StateDB)
	}
	
	// Start caching if not active
	if !cacheManager.IsActive() {
		cacheManager.StartEpochCaching(epoch)
	}
}

// FlushCacheAtEpochEnd flushes cache at the end of epoch processing
func FlushCacheAtEpochEnd() {
	cacheManager := GetCacheManager()
	cacheManager.FlushAndStopCaching()
}
