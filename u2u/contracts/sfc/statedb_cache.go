package sfc

import (
	"math/big"
	"sync"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/types"
	"github.com/unicornultrafoundation/go-u2u/core/vm"
	"github.com/unicornultrafoundation/go-u2u/log"
)

// StateDBCacheEntry represents a cached storage entry
type StateDBCacheEntry struct {
	Value     common.Hash
	IsDirty   bool   // True if value has been written to cache but not yet to StateDB
	ReadCount uint64 // Number of times this slot has been read from cache
}

// StateDBCache provides caching for evm.SfcStateDB operations
type StateDBCache struct {
	// Underlying StateDB
	stateDB vm.StateDB

	// Cache storage: contract address -> slot -> cached entry
	cache map[common.Address]map[common.Hash]*StateDBCacheEntry

	// Cache statistics
	readHits    uint64
	readMisses  uint64
	writeHits   uint64
	writeMisses uint64

	// Epoch management
	currentEpoch uint64
	isActive     bool

	// Thread safety
	mutex sync.RWMutex

	// Cache configuration
	maxCacheSize int // Maximum number of entries per contract
}

// NewStateDBCache creates a new StateDB cache wrapper
func NewStateDBCache(stateDB vm.StateDB) *StateDBCache {
	return &StateDBCache{
		stateDB:      stateDB,
		cache:        make(map[common.Address]map[common.Hash]*StateDBCacheEntry),
		maxCacheSize: 10000, // Configurable cache size
		isActive:     false,
	}
}

// StartEpochCaching activates caching for the given epoch
func (c *StateDBCache) StartEpochCaching(epoch uint64) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.currentEpoch = epoch
	c.isActive = true

	// Clear previous cache
	c.cache = make(map[common.Address]map[common.Hash]*StateDBCacheEntry)
	c.readHits = 0
	c.readMisses = 0
	c.writeHits = 0
	c.writeMisses = 0

	log.Info("StateDB cache activated for epoch", "epoch", epoch)
}

// GetState retrieves a value from cache or underlying StateDB
func (c *StateDBCache) GetState(addr common.Address, slot common.Hash) common.Hash {
	if !c.isActive {
		return c.stateDB.GetState(addr, slot)
	}

	c.mutex.RLock()

	// Check if contract exists in cache
	contractCache, contractExists := c.cache[addr]
	if contractExists {
		// Check if slot exists in contract cache
		if entry, slotExists := contractCache[slot]; slotExists {
			entry.ReadCount++
			c.readHits++
			c.mutex.RUnlock()

			log.Debug("StateDB cache hit", "addr", addr.Hex(), "slot", slot.Hex(), "value", entry.Value.Hex())
			return entry.Value
		}
	}

	c.mutex.RUnlock()

	// Cache miss - read from underlying StateDB
	value := c.stateDB.GetState(addr, slot)

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Initialize contract cache if needed
	if !contractExists {
		c.cache[addr] = make(map[common.Hash]*StateDBCacheEntry)
	}

	// Store in cache
	c.cache[addr][slot] = &StateDBCacheEntry{
		Value:     value,
		IsDirty:   false,
		ReadCount: 1,
	}

	c.readMisses++

	log.Debug("StateDB cache miss", "addr", addr.Hex(), "slot", slot.Hex(), "value", value.Hex())
	return value
}

// SetState stores a value in cache (write-through to StateDB at epoch end)
func (c *StateDBCache) SetState(addr common.Address, slot common.Hash, value common.Hash) {
	if !c.isActive {
		c.stateDB.SetState(addr, slot, value)
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Initialize contract cache if needed
	if _, exists := c.cache[addr]; !exists {
		c.cache[addr] = make(map[common.Hash]*StateDBCacheEntry)
	}

	// Check if this is updating an existing cached entry
	if entry, exists := c.cache[addr][slot]; exists {
		entry.Value = value
		entry.IsDirty = true
		c.writeHits++
		log.Debug("StateDB cache write hit", "addr", addr.Hex(), "slot", slot.Hex(), "value", value.Hex())
	} else {
		// New entry
		c.cache[addr][slot] = &StateDBCacheEntry{
			Value:     value,
			IsDirty:   true,
			ReadCount: 0,
		}
		c.writeMisses++
		log.Debug("StateDB cache write miss", "addr", addr.Hex(), "slot", slot.Hex(), "value", value.Hex())
	}

	// Check cache size limits
	c.evictIfNeeded(addr)
}

// evictIfNeeded removes least recently used entries if cache is too large
func (c *StateDBCache) evictIfNeeded(addr common.Address) {
	contractCache := c.cache[addr]
	if len(contractCache) <= c.maxCacheSize {
		return
	}

	// Find entry with lowest read count (LRU approximation)
	var minReadCount uint64 = ^uint64(0)
	var evictSlot common.Hash

	for slot, entry := range contractCache {
		if entry.ReadCount < minReadCount {
			minReadCount = entry.ReadCount
			evictSlot = slot
		}
	}

	// Write dirty entry to StateDB before eviction
	if entry := contractCache[evictSlot]; entry.IsDirty {
		c.stateDB.SetState(addr, evictSlot, entry.Value)
		log.Debug("StateDB cache eviction write", "addr", addr.Hex(), "slot", evictSlot.Hex())
	}

	delete(contractCache, evictSlot)
	log.Debug("StateDB cache eviction", "addr", addr.Hex(), "slot", evictSlot.Hex())
}

// FlushToStateDB writes all dirty cache entries to the underlying StateDB
func (c *StateDBCache) FlushToStateDB() {
	if !c.isActive {
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	dirtyCount := 0

	// Write all dirty entries to StateDB
	for addr, contractCache := range c.cache {
		for slot, entry := range contractCache {
			if entry.IsDirty {
				c.stateDB.SetState(addr, slot, entry.Value)
				entry.IsDirty = false
				dirtyCount++

				log.Debug("StateDB cache flush", "addr", addr.Hex(), "slot", slot.Hex(), "value", entry.Value.Hex())
			}
		}
	}

	log.Info("StateDB cache flushed to StateDB",
		"epoch", c.currentEpoch,
		"dirtyEntries", dirtyCount,
		"readHits", c.readHits,
		"readMisses", c.readMisses,
		"writeHits", c.writeHits,
		"writeMisses", c.writeMisses,
		"hitRate", float64(c.readHits)/float64(c.readHits+c.readMisses)*100)
}

// StopEpochCaching deactivates caching and flushes all dirty entries
func (c *StateDBCache) StopEpochCaching() {
	if !c.isActive {
		return
	}

	// Flush all dirty entries to StateDB
	c.FlushToStateDB()

	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.isActive = false

	// Clear cache to free memory
	c.cache = make(map[common.Address]map[common.Hash]*StateDBCacheEntry)

	log.Info("StateDB cache deactivated and flushed", "epoch", c.currentEpoch)
}

// GetCacheStats returns current cache statistics
func (c *StateDBCache) GetCacheStats() (readHits, readMisses, writeHits, writeMisses uint64, hitRate float64) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	totalReads := c.readHits + c.readMisses
	if totalReads > 0 {
		hitRate = float64(c.readHits) / float64(totalReads) * 100
	}

	return c.readHits, c.readMisses, c.writeHits, c.writeMisses, hitRate
}

// IsActive returns whether caching is currently active
func (c *StateDBCache) IsActive() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.isActive
}

// GetCurrentEpoch returns the current cached epoch
func (c *StateDBCache) GetCurrentEpoch() uint64 {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.currentEpoch
}

// Delegate all other StateDB methods to the underlying StateDB
func (c *StateDBCache) CreateAccount(addr common.Address) {
	c.stateDB.CreateAccount(addr)
}

func (c *StateDBCache) SubBalance(addr common.Address, amount *big.Int) {
	c.stateDB.SubBalance(addr, amount)
}

func (c *StateDBCache) AddBalance(addr common.Address, amount *big.Int) {
	c.stateDB.AddBalance(addr, amount)
}

func (c *StateDBCache) GetBalance(addr common.Address) *big.Int {
	return c.stateDB.GetBalance(addr)
}

func (c *StateDBCache) GetNonce(addr common.Address) uint64 {
	return c.stateDB.GetNonce(addr)
}

func (c *StateDBCache) SetNonce(addr common.Address, nonce uint64) {
	c.stateDB.SetNonce(addr, nonce)
}

func (c *StateDBCache) GetCodeHash(addr common.Address) common.Hash {
	return c.stateDB.GetCodeHash(addr)
}

func (c *StateDBCache) GetCode(addr common.Address) []byte {
	return c.stateDB.GetCode(addr)
}

func (c *StateDBCache) SetCode(addr common.Address, code []byte) {
	c.stateDB.SetCode(addr, code)
}

func (c *StateDBCache) GetCodeSize(addr common.Address) int {
	return c.stateDB.GetCodeSize(addr)
}

func (c *StateDBCache) AddRefund(gas uint64) {
	c.stateDB.AddRefund(gas)
}

func (c *StateDBCache) SubRefund(gas uint64) {
	c.stateDB.SubRefund(gas)
}

func (c *StateDBCache) GetRefund() uint64 {
	return c.stateDB.GetRefund()
}

func (c *StateDBCache) GetCommittedState(addr common.Address, slot common.Hash) common.Hash {
	return c.stateDB.GetCommittedState(addr, slot)
}

func (c *StateDBCache) Suicide(addr common.Address) bool {
	return c.stateDB.Suicide(addr)
}

func (c *StateDBCache) HasSuicided(addr common.Address) bool {
	return c.stateDB.HasSuicided(addr)
}

func (c *StateDBCache) Exist(addr common.Address) bool {
	return c.stateDB.Exist(addr)
}

func (c *StateDBCache) Empty(addr common.Address) bool {
	return c.stateDB.Empty(addr)
}

func (c *StateDBCache) PrepareAccessList(sender common.Address, dest *common.Address, precompiles []common.Address, txAccesses types.AccessList) {
	c.stateDB.PrepareAccessList(sender, dest, precompiles, txAccesses)
}

func (c *StateDBCache) AddressInAccessList(addr common.Address) bool {
	return c.stateDB.AddressInAccessList(addr)
}

func (c *StateDBCache) SlotInAccessList(addr common.Address, slot common.Hash) (addressOk bool, slotOk bool) {
	return c.stateDB.SlotInAccessList(addr, slot)
}

func (c *StateDBCache) AddAddressToAccessList(addr common.Address) {
	c.stateDB.AddAddressToAccessList(addr)
}

func (c *StateDBCache) AddSlotToAccessList(addr common.Address, slot common.Hash) {
	c.stateDB.AddSlotToAccessList(addr, slot)
}

func (c *StateDBCache) RevertToSnapshot(id int) {
	c.stateDB.RevertToSnapshot(id)
}

func (c *StateDBCache) Snapshot() int {
	return c.stateDB.Snapshot()
}

func (c *StateDBCache) AddLog(log *types.Log) {
	c.stateDB.AddLog(log)
}

func (c *StateDBCache) AddPreimage(hash common.Hash, preimage []byte) {
	c.stateDB.AddPreimage(hash, preimage)
}

func (c *StateDBCache) ForEachStorage(addr common.Address, cb func(common.Hash, common.Hash) bool) error {
	return c.stateDB.ForEachStorage(addr, cb)
}

func (c *StateDBCache) GetStorageRoot(addr common.Address) common.Hash {
	return c.stateDB.GetStorageRoot(addr)
}
