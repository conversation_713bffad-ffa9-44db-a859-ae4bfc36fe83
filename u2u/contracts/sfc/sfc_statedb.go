package sfc

import (
	"container/list"
	"sync"
	"sync/atomic"
	"time"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/state"
	"github.com/unicornultrafoundation/go-u2u/log"
)

// cacheKey represents a unique key for cache entries
type cacheKey struct {
	addr common.Address
	slot common.Hash
}

// lruEntry represents an entry in the LRU list
type lruEntry struct {
	key   cacheKey
	entry *SfcCacheEntry
}

// EpochCacheStats represents cache statistics for a specific epoch
type EpochCacheStats struct {
	EpochNumber uint64
	StartTime   time.Time
	EndTime     time.Time
	Duration    time.Duration

	// Operation counts
	ReadHits        uint64
	ReadMisses      uint64
	WriteHits       uint64
	WriteMisses     uint64
	TotalOperations uint64

	// Hit rates
	ReadHitRate    float64
	WriteHitRate   float64
	OverallHitRate float64

	// Cache efficiency
	MaxCacheEntries int
	AvgCacheEntries float64
	CacheEvictions  uint64

	// Performance metrics
	EstimatedGasSaved uint64
	AvgOpLatency      time.Duration

	// Contract-specific stats
	ContractStats map[common.Address]*ContractCacheStats
}

// ContractCacheStats represents cache statistics for a specific contract
type ContractCacheStats struct {
	Address          common.Address
	CacheEntries     int
	DirtyEntries     int
	TotalReads       uint64
	TotalWrites      uint64
	AvgReadsPerEntry float64
}

// SfcCacheEntry represents a cached storage entry
type SfcCacheEntry struct {
	Value     common.Hash
	IsDirty   bool
	ReadCount uint64
	LastRead  time.Time
}

// SfcStateDB is an enhanced StateDB with built-in caching for SFC operations
type SfcStateDB struct {
	*state.StateDB // Embed the original StateDB

	// Cache configuration
	cacheEnabled bool
	maxCacheSize int
	currentEpoch uint64

	// Cache storage: contract address -> slot -> cached entry
	cache map[common.Address]map[common.Hash]*SfcCacheEntry
	mutex sync.RWMutex

	// Cache statistics (atomic for better performance)
	readHits    uint64
	readMisses  uint64
	writeHits   uint64
	writeMisses uint64

	// Periodic logging
	lastLoggedOps uint64
	logInterval   uint64

	// Optimization fields
	totalCacheEntries int                        // Track total entries to avoid counting
	lruList           *list.List                 // Doubly linked list for O(1) LRU operations
	lruMap            map[cacheKey]*list.Element // Map for O(1) LRU lookups

	// Pre-allocated pools for reducing GC pressure
	entryPool    sync.Pool
	cacheKeyPool sync.Pool

	// Async LRU updates for better concurrency
	lruUpdateChan  chan cacheKey
	lruUpdaterDone chan struct{}

	// Epoch-based statistics tracking
	epochStartTime  time.Time
	epochStats      *EpochCacheStats
	allEpochStats   []*EpochCacheStats
	epochStatsMutex sync.RWMutex
}

// NewSfcStateDB creates a new SfcStateDB with caching capabilities
func NewSfcStateDB(stateDB *state.StateDB) *SfcStateDB {
	sfc := &SfcStateDB{
		StateDB:           stateDB,
		cacheEnabled:      false,
		maxCacheSize:      10000,
		cache:             make(map[common.Address]map[common.Hash]*SfcCacheEntry),
		logInterval:       1000,
		totalCacheEntries: 0,
		lruList:           list.New(),
		lruMap:            make(map[cacheKey]*list.Element),
		lruUpdateChan:     make(chan cacheKey, 1000),
		lruUpdaterDone:    make(chan struct{}),
		allEpochStats:     make([]*EpochCacheStats, 0),
	}

	// Initialize entry pool for reducing GC pressure
	sfc.entryPool = sync.Pool{
		New: func() interface{} {
			return &SfcCacheEntry{}
		},
	}

	// Initialize cache key pool
	sfc.cacheKeyPool = sync.Pool{
		New: func() interface{} {
			return &cacheKey{}
		},
	}

	// Start async LRU updater
	go sfc.asyncLRUUpdater()

	return sfc
}

// asyncLRUUpdater runs in a separate goroutine to handle LRU updates asynchronously
func (sfc *SfcStateDB) asyncLRUUpdater() {
	for {
		select {
		case key := <-sfc.lruUpdateChan:
			sfc.mutex.Lock()
			if element, exists := sfc.lruMap[key]; exists {
				sfc.lruList.MoveToFront(element)
			}
			sfc.mutex.Unlock()
		case <-sfc.lruUpdaterDone:
			return
		}
	}
}

// EnableCache activates caching for the given epoch
func (sfc *SfcStateDB) EnableCache(epoch uint64) {
	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()

	sfc.currentEpoch = epoch
	sfc.cacheEnabled = true

	// Clear previous cache
	sfc.cache = make(map[common.Address]map[common.Hash]*SfcCacheEntry)
	sfc.readHits = 0
	sfc.readMisses = 0
	sfc.writeHits = 0
	sfc.writeMisses = 0
	sfc.lastLoggedOps = 0

	// Reset optimization fields
	sfc.totalCacheEntries = 0
	sfc.lruList = list.New()
	sfc.lruMap = make(map[cacheKey]*list.Element)

	// Initialize epoch statistics tracking
	sfc.epochStartTime = time.Now()
	sfc.epochStats = &EpochCacheStats{
		EpochNumber:     epoch,
		StartTime:       sfc.epochStartTime,
		MaxCacheEntries: sfc.maxCacheSize,
		ContractStats:   make(map[common.Address]*ContractCacheStats),
	}

	log.Info("=== SFC CACHE ACTIVATED ===",
		"epoch", epoch,
		"maxCacheSize", sfc.maxCacheSize,
		"cacheReady", true)
}

// DisableCache deactivates caching and flushes all dirty entries
func (sfc *SfcStateDB) DisableCache() {
	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()

	if !sfc.cacheEnabled {
		return
	}

	// Finalize epoch statistics before flushing
	sfc.finalizeEpochStats()

	// Flush all dirty entries before disabling
	sfc.flushDirtyEntries()

	// Log comprehensive epoch statistics
	sfc.logEpochCacheStatistics()

	// Log final statistics (legacy)
	sfc.logFinalStatistics()

	sfc.cacheEnabled = false
	sfc.cache = make(map[common.Address]map[common.Hash]*SfcCacheEntry)

	log.Info("SFC cache disabled successfully", "epoch", sfc.currentEpoch)
}

// GetState retrieves a value from the given contract and slot with caching
func (sfc *SfcStateDB) GetState(addr common.Address, slot common.Hash) common.Hash {
	// Fast path for disabled cache - completely bypass all cache logic
	if !sfc.cacheEnabled {
		return sfc.StateDB.GetState(addr, slot)
	}

	// Try read-only path first (most common case)
	sfc.mutex.RLock()
	if contractCache, exists := sfc.cache[addr]; exists {
		if entry, found := contractCache[slot]; found {
			// Cache hit - get value while holding read lock
			value := entry.Value
			sfc.mutex.RUnlock()

			// Update stats atomically (no lock needed)
			atomic.AddUint64(&sfc.readHits, 1)

			return value
		}
	}
	sfc.mutex.RUnlock()

	// Cache miss - handle with write lock
	return sfc.handleCacheMiss(addr, slot)
}

// handleCacheMiss handles cache miss scenarios with write lock
func (sfc *SfcStateDB) handleCacheMiss(addr common.Address, slot common.Hash) common.Hash {
	value := sfc.StateDB.GetState(addr, slot)

	sfc.mutex.Lock()
	atomic.AddUint64(&sfc.readMisses, 1)
	sfc.addToCacheOptimized(addr, slot, value, false)
	sfc.mutex.Unlock()

	return value
}

// SetState sets a value for the given contract and slot with caching
func (sfc *SfcStateDB) SetState(addr common.Address, slot common.Hash, value common.Hash) {
	// Fast path for disabled cache - completely bypass all cache logic
	if !sfc.cacheEnabled {
		sfc.StateDB.SetState(addr, slot, value)
		return
	}

	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()

	// Check if entry exists in cache
	if contractCache, exists := sfc.cache[addr]; exists {
		if entry, found := contractCache[slot]; found {
			// Cache hit - update existing entry
			entry.Value = value
			entry.IsDirty = true
			atomic.AddUint64(&sfc.writeHits, 1)
			return
		}
	}

	// Cache miss - add new entry
	sfc.addToCacheOptimized(addr, slot, value, true)
	atomic.AddUint64(&sfc.writeMisses, 1)
}

// updateLRU updates the LRU position of a cache entry
func (sfc *SfcStateDB) updateLRU(key cacheKey) {
	if element, exists := sfc.lruMap[key]; exists {
		sfc.lruList.MoveToFront(element)
	}
}

// addToCacheOptimized adds an entry to the cache with optimized LRU management
func (sfc *SfcStateDB) addToCacheOptimized(addr common.Address, slot common.Hash, value common.Hash, isDirty bool) {
	// Check if eviction is needed
	if sfc.totalCacheEntries >= sfc.maxCacheSize {
		sfc.evictLRUOptimized()
	}

	// Get or create cache entry
	entry := sfc.getPooledEntry()
	entry.Value = value
	entry.IsDirty = isDirty
	entry.ReadCount = 1

	// Ensure contract cache exists
	if _, exists := sfc.cache[addr]; !exists {
		sfc.cache[addr] = make(map[common.Hash]*SfcCacheEntry)
	}
	sfc.cache[addr][slot] = entry

	// Add to LRU structures
	key := cacheKey{addr: addr, slot: slot}
	lruEntry := &lruEntry{key: key, entry: entry}
	element := sfc.lruList.PushFront(lruEntry)
	sfc.lruMap[key] = element
	sfc.totalCacheEntries++
}

// getPooledEntry gets a cache entry from the pool or creates a new one
func (sfc *SfcStateDB) getPooledEntry() *SfcCacheEntry {
	entry := sfc.entryPool.Get().(*SfcCacheEntry)
	// Reset entry fields
	entry.Value = common.Hash{}
	entry.IsDirty = false
	entry.ReadCount = 0
	return entry
}

// evictLRUOptimized removes the least recently used entry with O(1) complexity
func (sfc *SfcStateDB) evictLRUOptimized() {
	if sfc.lruList.Len() == 0 {
		return
	}

	// Get least recently used entry (back of list)
	element := sfc.lruList.Back()
	if element == nil {
		return
	}

	lruEntry := element.Value.(*lruEntry)
	key := lruEntry.key
	entry := lruEntry.entry

	// Flush if dirty before evicting
	if entry.IsDirty {
		sfc.StateDB.SetState(key.addr, key.slot, entry.Value)
	}

	// Remove from all data structures
	delete(sfc.cache[key.addr], key.slot)
	if len(sfc.cache[key.addr]) == 0 {
		delete(sfc.cache, key.addr)
	}
	delete(sfc.lruMap, key)
	sfc.lruList.Remove(element)
	sfc.totalCacheEntries--

	// Return entry to pool
	sfc.entryPool.Put(entry)
}

// evictLRUEntry removes the least recently used entry (simplified)
func (sfc *SfcStateDB) evictLRUEntry() {
	// Simple eviction: remove first entry found
	for addr, contractCache := range sfc.cache {
		for slot, entry := range contractCache {
			// Flush if dirty before evicting
			if entry.IsDirty {
				sfc.StateDB.SetState(addr, slot, entry.Value)
			}

			// Remove from cache
			delete(contractCache, slot)
			if len(contractCache) == 0 {
				delete(sfc.cache, addr)
			}
			return
		}
	}
}

// flushDirtyEntries writes all dirty cache entries to StateDB
func (sfc *SfcStateDB) flushDirtyEntries() {
	dirtyCount := 0
	contractStats := make(map[common.Address]struct {
		entries int
		dirty   int
		reads   int
	})

	// Write all dirty entries to StateDB and collect statistics
	for addr, contractCache := range sfc.cache {
		stats := contractStats[addr]
		stats.entries = len(contractCache)

		for slot, entry := range contractCache {
			stats.reads += int(entry.ReadCount)

			if entry.IsDirty {
				sfc.StateDB.SetState(addr, slot, entry.Value)
				entry.IsDirty = false
				dirtyCount++
				stats.dirty++
			}
		}
		contractStats[addr] = stats
	}

	// Calculate comprehensive statistics
	totalReads := sfc.readHits + sfc.readMisses
	totalWrites := sfc.writeHits + sfc.writeMisses
	totalOperations := totalReads + totalWrites

	var readHitRate, writeHitRate, overallHitRate float64
	if totalReads > 0 {
		readHitRate = float64(sfc.readHits) / float64(totalReads) * 100
	}
	if totalWrites > 0 {
		writeHitRate = float64(sfc.writeHits) / float64(totalWrites) * 100
	}
	if totalOperations > 0 {
		overallHitRate = float64(sfc.readHits+sfc.writeHits) / float64(totalOperations) * 100
	}

	// Log comprehensive epoch cache statistics
	log.Info("=== SFC CACHE STATISTICS ===",
		"epoch", sfc.currentEpoch,
		"totalOperations", totalOperations,
		"cacheEntries", sfc.totalCacheEntries,
		"dirtyEntries", dirtyCount)

	log.Info("Read Operations",
		"readHits", sfc.readHits,
		"readMisses", sfc.readMisses,
		"readHitRate", readHitRate)

	log.Info("Write Operations",
		"writeHits", sfc.writeHits,
		"writeMisses", sfc.writeMisses,
		"writeHitRate", writeHitRate)

	log.Info("Overall Performance",
		"overallHitRate", overallHitRate,
		"memoryEfficiency", float64(sfc.totalCacheEntries)/float64(totalOperations)*100)

	// Log per-contract statistics
	for addr, stats := range contractStats {
		if stats.entries > 0 {
			log.Info("Contract Cache Stats",
				"contract", addr.Hex(),
				"cacheEntries", stats.entries,
				"dirtyEntries", stats.dirty,
				"totalReads", stats.reads,
				"avgReadsPerEntry", float64(stats.reads)/float64(stats.entries))
		}
	}

	log.Info("SFC cache flushed to StateDB - Epoch Complete")
}

// checkPeriodicLogging logs cache statistics periodically during operations
func (sfc *SfcStateDB) checkPeriodicLogging() {
	readHits := atomic.LoadUint64(&sfc.readHits)
	readMisses := atomic.LoadUint64(&sfc.readMisses)
	writeHits := atomic.LoadUint64(&sfc.writeHits)
	writeMisses := atomic.LoadUint64(&sfc.writeMisses)
	totalOps := readHits + readMisses + writeHits + writeMisses

	if totalOps > 0 && totalOps%sfc.logInterval == 0 && totalOps != sfc.lastLoggedOps {
		sfc.lastLoggedOps = totalOps

		// Calculate current hit rates
		totalReads := readHits + readMisses
		totalWrites := writeHits + writeMisses

		var readHitRate, writeHitRate, overallHitRate float64
		if totalReads > 0 {
			readHitRate = float64(readHits) / float64(totalReads) * 100
		}
		if totalWrites > 0 {
			writeHitRate = float64(writeHits) / float64(totalWrites) * 100
		}
		if totalOps > 0 {
			overallHitRate = float64(readHits+writeHits) / float64(totalOps) * 100
		}

		log.Info("SFC Cache Progress Update",
			"epoch", sfc.currentEpoch,
			"totalOps", totalOps,
			"cacheEntries", sfc.totalCacheEntries,
			"readHitRate", readHitRate,
			"writeHitRate", writeHitRate,
			"overallHitRate", overallHitRate)
	}
}

// logFinalStatistics logs comprehensive final statistics
func (sfc *SfcStateDB) logFinalStatistics() {
	readHits := atomic.LoadUint64(&sfc.readHits)
	readMisses := atomic.LoadUint64(&sfc.readMisses)
	writeHits := atomic.LoadUint64(&sfc.writeHits)
	writeMisses := atomic.LoadUint64(&sfc.writeMisses)

	totalReads := readHits + readMisses
	totalWrites := writeHits + writeMisses
	totalOperations := totalReads + totalWrites

	var readHitRate, writeHitRate, overallHitRate float64
	if totalReads > 0 {
		readHitRate = float64(readHits) / float64(totalReads) * 100
	}
	if totalWrites > 0 {
		writeHitRate = float64(writeHits) / float64(totalWrites) * 100
	}
	if totalOperations > 0 {
		overallHitRate = float64(readHits+writeHits) / float64(totalOperations) * 100
	}

	// Calculate estimated gas savings
	readGasSaved := readHits * 2100    // SLOAD gas cost
	writeGasSaved := writeHits * 20000 // SSTORE gas cost
	totalGasSaved := readGasSaved + writeGasSaved

	// Log comprehensive final statistics
	log.Info("=== SFC CACHE FINAL SUMMARY ===",
		"epoch", sfc.currentEpoch,
		"totalOperations", totalOperations,
		"overallHitRate", overallHitRate)

	log.Info("Cache Performance Breakdown",
		"readHits", readHits,
		"readMisses", readMisses,
		"readHitRate", readHitRate,
		"writeHits", writeHits,
		"writeMisses", writeMisses,
		"writeHitRate", writeHitRate)

	log.Info("Gas Savings Estimate",
		"readGasSaved", readGasSaved,
		"writeGasSaved", writeGasSaved,
		"totalGasSaved", totalGasSaved,
		"avgGasSavedPerOp", float64(totalGasSaved)/float64(totalOperations))
}

// GetCacheStats returns current cache statistics
func (sfc *SfcStateDB) GetCacheStats() (readHits, readMisses, writeHits, writeMisses uint64, hitRate float64) {
	readHits = atomic.LoadUint64(&sfc.readHits)
	readMisses = atomic.LoadUint64(&sfc.readMisses)
	writeHits = atomic.LoadUint64(&sfc.writeHits)
	writeMisses = atomic.LoadUint64(&sfc.writeMisses)

	totalOps := readHits + readMisses + writeHits + writeMisses
	if totalOps > 0 {
		hitRate = float64(readHits+writeHits) / float64(totalOps) * 100
	}

	return readHits, readMisses, writeHits, writeMisses, hitRate
}

// IsCacheEnabled returns whether caching is currently enabled
func (sfc *SfcStateDB) IsCacheEnabled() bool {
	sfc.mutex.RLock()
	defer sfc.mutex.RUnlock()
	return sfc.cacheEnabled
}

// SetCacheSize sets the maximum cache size
func (sfc *SfcStateDB) SetCacheSize(size int) {
	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()
	sfc.maxCacheSize = size
}

// Global SfcStateDB cache manager
var (
	globalSfcCache *SfcStateDB
	cacheMutex     sync.RWMutex
)

// GetOrCreateSfcStateDB returns the global SfcStateDB instance or creates one
func GetOrCreateSfcStateDB(stateDB *state.StateDB) *SfcStateDB {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	if globalSfcCache == nil || globalSfcCache.StateDB != stateDB {
		globalSfcCache = NewSfcStateDB(stateDB)
	}

	return globalSfcCache
}

// EnableSfcCacheForEpoch enables caching for the given epoch
func EnableSfcCacheForEpoch(stateDB *state.StateDB, epoch uint64) *SfcStateDB {
	sfcStateDB := GetOrCreateSfcStateDB(stateDB)
	sfcStateDB.EnableCache(epoch)
	return sfcStateDB
}

// DisableSfcCache disables caching and flushes all dirty entries
func DisableSfcCache() {
	cacheMutex.RLock()
	defer cacheMutex.RUnlock()

	if globalSfcCache != nil {
		globalSfcCache.DisableCache()
	}
}

// WithSfcCache executes a function with SFC caching enabled
func WithSfcCache(stateDB *state.StateDB, epoch uint64, fn func(*SfcStateDB) error) error {
	sfcStateDB := EnableSfcCacheForEpoch(stateDB, epoch)
	defer sfcStateDB.DisableCache()

	return fn(sfcStateDB)
}

// Helper functions for easy integration

// StartEpochCaching starts caching for epoch sealing operations
func StartEpochCaching(stateDB *state.StateDB, epoch uint64) *SfcStateDB {
	return EnableSfcCacheForEpoch(stateDB, epoch)
}

// StopEpochCaching stops caching and flushes all changes
func StopEpochCaching() {
	DisableSfcCache()
}

// WrapStateDBForSFC wraps a regular StateDB with enhanced SfcStateDB if needed
func WrapStateDBForSFC(stateDB interface{}) interface{} {
	if stateDB == nil {
		return nil
	}

	// Check if it's already an enhanced SfcStateDB
	if _, ok := stateDB.(interface{ EnableCache(uint64) }); ok {
		return stateDB
	}

	// Check if it's a regular StateDB that can be wrapped
	if regularStateDB, ok := stateDB.(*state.StateDB); ok {
		return NewSfcStateDB(regularStateDB)
	}

	// Return as-is if we can't wrap it
	return stateDB
}

// finalizeEpochStats calculates final statistics for the current epoch
func (sfc *SfcStateDB) finalizeEpochStats() {
	if sfc.epochStats == nil {
		return
	}

	// Finalize timing
	sfc.epochStats.EndTime = time.Now()
	sfc.epochStats.Duration = sfc.epochStats.EndTime.Sub(sfc.epochStats.StartTime)

	// Get atomic statistics
	sfc.epochStats.ReadHits = atomic.LoadUint64(&sfc.readHits)
	sfc.epochStats.ReadMisses = atomic.LoadUint64(&sfc.readMisses)
	sfc.epochStats.WriteHits = atomic.LoadUint64(&sfc.writeHits)
	sfc.epochStats.WriteMisses = atomic.LoadUint64(&sfc.writeMisses)
	sfc.epochStats.TotalOperations = sfc.epochStats.ReadHits + sfc.epochStats.ReadMisses +
		sfc.epochStats.WriteHits + sfc.epochStats.WriteMisses

	// Calculate hit rates
	totalReads := sfc.epochStats.ReadHits + sfc.epochStats.ReadMisses
	totalWrites := sfc.epochStats.WriteHits + sfc.epochStats.WriteMisses

	if totalReads > 0 {
		sfc.epochStats.ReadHitRate = float64(sfc.epochStats.ReadHits) / float64(totalReads) * 100
	}
	if totalWrites > 0 {
		sfc.epochStats.WriteHitRate = float64(sfc.epochStats.WriteHits) / float64(totalWrites) * 100
	}
	if sfc.epochStats.TotalOperations > 0 {
		sfc.epochStats.OverallHitRate = float64(sfc.epochStats.ReadHits+sfc.epochStats.WriteHits) /
			float64(sfc.epochStats.TotalOperations) * 100
	}

	// Calculate cache efficiency metrics
	sfc.epochStats.AvgCacheEntries = float64(sfc.totalCacheEntries)
	sfc.epochStats.EstimatedGasSaved = sfc.epochStats.ReadHits*2100 + sfc.epochStats.WriteHits*20000

	// Calculate average operation latency (estimated)
	if sfc.epochStats.TotalOperations > 0 {
		sfc.epochStats.AvgOpLatency = sfc.epochStats.Duration / time.Duration(sfc.epochStats.TotalOperations)
	}

	// Collect contract-specific statistics
	sfc.collectContractStats()

	// Store epoch stats in history
	sfc.epochStatsMutex.Lock()
	sfc.allEpochStats = append(sfc.allEpochStats, sfc.epochStats)
	sfc.epochStatsMutex.Unlock()
}

// collectContractStats collects statistics for each contract
func (sfc *SfcStateDB) collectContractStats() {
	if sfc.epochStats == nil {
		return
	}

	for addr, contractCache := range sfc.cache {
		stats := &ContractCacheStats{
			Address:      addr,
			CacheEntries: len(contractCache),
		}

		totalReads := uint64(0)
		totalWrites := uint64(0)
		dirtyCount := 0

		for _, entry := range contractCache {
			totalReads += entry.ReadCount
			if entry.IsDirty {
				dirtyCount++
				totalWrites++
			}
		}

		stats.TotalReads = totalReads
		stats.TotalWrites = totalWrites
		stats.DirtyEntries = dirtyCount

		if stats.CacheEntries > 0 {
			stats.AvgReadsPerEntry = float64(totalReads) / float64(stats.CacheEntries)
		}

		sfc.epochStats.ContractStats[addr] = stats
	}
}

// logEpochCacheStatistics logs comprehensive epoch cache statistics
func (sfc *SfcStateDB) logEpochCacheStatistics() {
	if sfc.epochStats == nil {
		return
	}

	stats := sfc.epochStats

	// Log epoch header
	log.Info("=== EPOCH CACHE STATISTICS ===",
		"epoch", stats.EpochNumber,
		"duration", stats.Duration,
		"startTime", stats.StartTime.Format("15:04:05.000"),
		"endTime", stats.EndTime.Format("15:04:05.000"))

	// Log operation statistics
	log.Info("Epoch Operation Summary",
		"totalOperations", stats.TotalOperations,
		"readHits", stats.ReadHits,
		"readMisses", stats.ReadMisses,
		"writeHits", stats.WriteHits,
		"writeMisses", stats.WriteMisses)

	// Log hit rates
	log.Info("Epoch Hit Rate Analysis",
		"readHitRate", stats.ReadHitRate,
		"writeHitRate", stats.WriteHitRate,
		"overallHitRate", stats.OverallHitRate)

	// Log cache efficiency
	log.Info("Epoch Cache Efficiency",
		"maxCacheEntries", stats.MaxCacheEntries,
		"avgCacheEntries", stats.AvgCacheEntries,
		"cacheEvictions", stats.CacheEvictions,
		"avgOpLatency", stats.AvgOpLatency)

	// Log performance metrics
	log.Info("Epoch Performance Metrics",
		"estimatedGasSaved", stats.EstimatedGasSaved,
		"gasSavedPerOp", float64(stats.EstimatedGasSaved)/float64(stats.TotalOperations),
		"opsPerSecond", float64(stats.TotalOperations)/stats.Duration.Seconds())

	// Log contract-specific statistics
	if len(stats.ContractStats) > 0 {
		log.Info("Epoch Contract Statistics", "contractCount", len(stats.ContractStats))

		for addr, contractStats := range stats.ContractStats {
			if contractStats.CacheEntries > 0 {
				log.Info("Contract Cache Performance",
					"contract", addr.Hex(),
					"cacheEntries", contractStats.CacheEntries,
					"dirtyEntries", contractStats.DirtyEntries,
					"totalReads", contractStats.TotalReads,
					"totalWrites", contractStats.TotalWrites,
					"avgReadsPerEntry", contractStats.AvgReadsPerEntry)
			}
		}
	}

	// Log epoch completion
	log.Info("=== EPOCH CACHE COMPLETE ===",
		"epoch", stats.EpochNumber,
		"overallHitRate", stats.OverallHitRate,
		"totalGasSaved", stats.EstimatedGasSaved,
		"cacheEfficiency", "SUCCESS")
}

// GetEpochStats returns statistics for a specific epoch
func (sfc *SfcStateDB) GetEpochStats(epoch uint64) *EpochCacheStats {
	sfc.epochStatsMutex.RLock()
	defer sfc.epochStatsMutex.RUnlock()

	for _, stats := range sfc.allEpochStats {
		if stats.EpochNumber == epoch {
			return stats
		}
	}
	return nil
}

// GetAllEpochStats returns statistics for all epochs
func (sfc *SfcStateDB) GetAllEpochStats() []*EpochCacheStats {
	sfc.epochStatsMutex.RLock()
	defer sfc.epochStatsMutex.RUnlock()

	// Return a copy to prevent external modification
	result := make([]*EpochCacheStats, len(sfc.allEpochStats))
	copy(result, sfc.allEpochStats)
	return result
}

// LogEpochSummary logs a summary of recent epoch performance
func (sfc *SfcStateDB) LogEpochSummary(recentEpochs int) {
	sfc.epochStatsMutex.RLock()
	defer sfc.epochStatsMutex.RUnlock()

	if len(sfc.allEpochStats) == 0 {
		return
	}

	// Get recent epochs
	start := len(sfc.allEpochStats) - recentEpochs
	if start < 0 {
		start = 0
	}
	recentStats := sfc.allEpochStats[start:]

	// Calculate aggregate statistics
	totalOps := uint64(0)
	totalGasSaved := uint64(0)
	totalHitRate := float64(0)

	for _, stats := range recentStats {
		totalOps += stats.TotalOperations
		totalGasSaved += stats.EstimatedGasSaved
		totalHitRate += stats.OverallHitRate
	}

	avgHitRate := totalHitRate / float64(len(recentStats))

	log.Info("=== RECENT EPOCHS SUMMARY ===",
		"epochCount", len(recentStats),
		"totalOperations", totalOps,
		"avgHitRate", avgHitRate,
		"totalGasSaved", totalGasSaved,
		"avgOpsPerEpoch", totalOps/uint64(len(recentStats)))
}

// DemonstrateEpochLogging demonstrates the epoch cache logging functionality
// This function can be called to show how the epoch logging works
func DemonstrateEpochLogging() {
	log.Info("=== EPOCH CACHE LOGGING DEMONSTRATION ===")
	log.Info("The epoch cache logging system provides comprehensive statistics after each epoch:")
	log.Info("1. Operation counts (read hits/misses, write hits/misses)")
	log.Info("2. Hit rate analysis (read, write, and overall hit rates)")
	log.Info("3. Cache efficiency metrics (evictions, memory usage)")
	log.Info("4. Performance metrics (gas savings, throughput, latency)")
	log.Info("5. Contract-specific statistics (per-contract cache behavior)")
	log.Info("6. Historical data storage for trend analysis")
	log.Info("")
	log.Info("Example epoch log output:")
	log.Info("INFO [timestamp] === EPOCH CACHE STATISTICS ===  epoch=150 duration=45.234s")
	log.Info("INFO [timestamp] Epoch Operation Summary         totalOperations=12567 readHits=8934 readMisses=2145")
	log.Info("INFO [timestamp] Epoch Hit Rate Analysis         readHitRate=80.65 writeHitRate=82.93 overallHitRate=81.12")
	log.Info("INFO [timestamp] Epoch Cache Efficiency          maxCacheEntries=10000 avgCacheEntries=3456.7")
	log.Info("INFO [timestamp] Epoch Performance Metrics       estimatedGasSaved=43567800 gasSavedPerOp=3467.2")
	log.Info("INFO [timestamp] Contract Cache Performance      contract=0xfc00... cacheEntries=2134 totalReads=8934")
	log.Info("INFO [timestamp] === EPOCH CACHE COMPLETE ===    epoch=150 overallHitRate=81.12 totalGasSaved=43567800")
	log.Info("")
	log.Info("This logging is automatically triggered when DisableCache() is called at the end of each epoch.")
	log.Info("The logs provide valuable insights for performance monitoring and optimization.")
	log.Info("=== DEMONSTRATION COMPLETE ===")
}
