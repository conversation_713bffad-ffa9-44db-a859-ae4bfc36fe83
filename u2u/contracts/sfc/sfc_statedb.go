package sfc

import (
	"container/list"
	"sync"
	"sync/atomic"
	"time"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/state"
	"github.com/unicornultrafoundation/go-u2u/log"
)

// cacheKey represents a unique key for cache entries
type cacheKey struct {
	addr common.Address
	slot common.Hash
}

// lruEntry represents an entry in the LRU list
type lruEntry struct {
	key   cacheKey
	entry *SfcCacheEntry
}

// SfcCacheEntry represents a cached storage entry
type SfcCacheEntry struct {
	Value     common.Hash
	IsDirty   bool
	ReadCount uint64
	LastRead  time.Time
}

// SfcStateDB is an enhanced StateDB with built-in caching for SFC operations
type SfcStateDB struct {
	*state.StateDB // Embed the original StateDB

	// Cache configuration
	cacheEnabled     bool
	cacheEnabledFlag uint32 // Atomic flag for lock-free cache enabled check
	maxCacheSize     int
	currentEpoch     uint64

	// Cache storage: contract address -> slot -> cached entry
	cache map[common.Address]map[common.Hash]*SfcCacheEntry
	mutex sync.RWMutex

	// Cache statistics (atomic for better performance)
	readHits    uint64
	readMisses  uint64
	writeHits   uint64
	writeMisses uint64

	// Periodic logging
	lastLoggedOps uint64
	logInterval   uint64

	// Optimization fields
	totalCacheEntries int                        // Track total entries to avoid counting
	lruList           *list.List                 // Doubly linked list for O(1) LRU operations
	lruMap            map[cacheKey]*list.Element // Map for O(1) LRU lookups

	// Pre-allocated pools for reducing GC pressure
	entryPool    sync.Pool
	cacheKeyPool sync.Pool

	// Async LRU updates for better concurrency
	lruUpdateChan  chan cacheKey
	lruUpdaterDone chan struct{}
}

// NewSfcStateDB creates a new SfcStateDB with caching capabilities
func NewSfcStateDB(stateDB *state.StateDB) *SfcStateDB {
	sfc := &SfcStateDB{
		StateDB:           stateDB,
		cacheEnabled:      false,
		cacheEnabledFlag:  0,
		maxCacheSize:      10000,
		cache:             make(map[common.Address]map[common.Hash]*SfcCacheEntry),
		logInterval:       1000,
		totalCacheEntries: 0,
		lruList:           list.New(),
		lruMap:            make(map[cacheKey]*list.Element),
		lruUpdateChan:     make(chan cacheKey, 1000),
		lruUpdaterDone:    make(chan struct{}),
	}

	// Initialize entry pool for reducing GC pressure
	sfc.entryPool = sync.Pool{
		New: func() interface{} {
			return &SfcCacheEntry{}
		},
	}

	// Initialize cache key pool
	sfc.cacheKeyPool = sync.Pool{
		New: func() interface{} {
			return &cacheKey{}
		},
	}

	// Start async LRU updater
	go sfc.asyncLRUUpdater()

	return sfc
}

// asyncLRUUpdater runs in a separate goroutine to handle LRU updates asynchronously
func (sfc *SfcStateDB) asyncLRUUpdater() {
	for {
		select {
		case key := <-sfc.lruUpdateChan:
			sfc.mutex.Lock()
			if element, exists := sfc.lruMap[key]; exists {
				sfc.lruList.MoveToFront(element)
			}
			sfc.mutex.Unlock()
		case <-sfc.lruUpdaterDone:
			return
		}
	}
}

// EnableCache activates caching for the given epoch
func (sfc *SfcStateDB) EnableCache(epoch uint64) {
	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()

	sfc.currentEpoch = epoch
	sfc.cacheEnabled = true
	atomic.StoreUint32(&sfc.cacheEnabledFlag, 1)

	// Clear previous cache
	sfc.cache = make(map[common.Address]map[common.Hash]*SfcCacheEntry)
	sfc.readHits = 0
	sfc.readMisses = 0
	sfc.writeHits = 0
	sfc.writeMisses = 0
	sfc.lastLoggedOps = 0

	// Reset optimization fields
	sfc.totalCacheEntries = 0
	sfc.lruList = list.New()
	sfc.lruMap = make(map[cacheKey]*list.Element)

	log.Info("=== SFC CACHE ACTIVATED ===",
		"epoch", epoch,
		"maxCacheSize", sfc.maxCacheSize,
		"cacheReady", true)
}

// DisableCache deactivates caching and flushes all dirty entries
func (sfc *SfcStateDB) DisableCache() {
	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()

	if !sfc.cacheEnabled {
		return
	}

	// Flush all dirty entries before disabling
	sfc.flushDirtyEntries()

	// Log final statistics
	sfc.logFinalStatistics()

	sfc.cacheEnabled = false
	atomic.StoreUint32(&sfc.cacheEnabledFlag, 0)
	sfc.cache = make(map[common.Address]map[common.Hash]*SfcCacheEntry)

	log.Info("SFC cache disabled successfully", "epoch", sfc.currentEpoch)
}

// GetState retrieves a value from the given contract and slot with caching
func (sfc *SfcStateDB) GetState(addr common.Address, slot common.Hash) common.Hash {
	// Fast path for disabled cache using atomic flag
	if atomic.LoadUint32(&sfc.cacheEnabledFlag) == 0 {
		return sfc.StateDB.GetState(addr, slot)
	}

	// Try read-only path first (most common case)
	sfc.mutex.RLock()
	if contractCache, exists := sfc.cache[addr]; exists {
		if entry, found := contractCache[slot]; found {
			// Cache hit - get value while holding read lock
			value := entry.Value
			sfc.mutex.RUnlock()

			// Update stats atomically (no lock needed)
			atomic.AddUint64(&sfc.readHits, 1)

			// Async LRU update (non-blocking)
			key := cacheKey{addr: addr, slot: slot}
			select {
			case sfc.lruUpdateChan <- key:
				// Successfully queued LRU update
			default:
				// Channel full, skip this update (acceptable for LRU)
			}

			// Debug logging
			log.Debug("SFC cache hit", "addr", addr.Hex(), "slot", slot.Hex(), "value", value.Hex())

			return value
		}
	}
	sfc.mutex.RUnlock()

	// Cache miss - handle with write lock
	return sfc.handleCacheMiss(addr, slot)
}

// handleCacheMiss handles cache miss scenarios with write lock
func (sfc *SfcStateDB) handleCacheMiss(addr common.Address, slot common.Hash) common.Hash {
	value := sfc.StateDB.GetState(addr, slot)

	sfc.mutex.Lock()
	atomic.AddUint64(&sfc.readMisses, 1)
	sfc.addToCacheOptimized(addr, slot, value, false)
	sfc.checkPeriodicLogging()
	sfc.mutex.Unlock()

	// Conditional debug logging
	log.Debug("SFC cache miss", "addr", addr.Hex(), "slot", slot.Hex(), "value", value.Hex())

	return value
}

// SetState sets a value for the given contract and slot with caching
func (sfc *SfcStateDB) SetState(addr common.Address, slot common.Hash, value common.Hash) {
	// Fast path for disabled cache using atomic flag
	if atomic.LoadUint32(&sfc.cacheEnabledFlag) == 0 {
		sfc.StateDB.SetState(addr, slot, value)
		return
	}

	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()

	// Check if entry exists in cache
	if contractCache, exists := sfc.cache[addr]; exists {
		if entry, found := contractCache[slot]; found {
			// Cache hit - update existing entry
			entry.Value = value
			entry.IsDirty = true
			entry.LastRead = time.Now()
			atomic.AddUint64(&sfc.writeHits, 1)

			// Async LRU update
			key := cacheKey{addr: addr, slot: slot}
			select {
			case sfc.lruUpdateChan <- key:
				// Successfully queued LRU update
			default:
				// Channel full, skip this update (acceptable for LRU)
			}

			log.Debug("SFC cache write hit", "addr", addr.Hex(), "slot", slot.Hex(), "value", value.Hex())
			return
		}
	}

	// Cache miss - add new entry
	sfc.addToCacheOptimized(addr, slot, value, true)
	atomic.AddUint64(&sfc.writeMisses, 1)

	log.Debug("SFC cache write miss", "addr", addr.Hex(), "slot", slot.Hex(), "value", value.Hex())
}

// updateLRU updates the LRU position of a cache entry
func (sfc *SfcStateDB) updateLRU(key cacheKey) {
	if element, exists := sfc.lruMap[key]; exists {
		sfc.lruList.MoveToFront(element)
	}
}

// addToCacheOptimized adds an entry to the cache with optimized LRU management
func (sfc *SfcStateDB) addToCacheOptimized(addr common.Address, slot common.Hash, value common.Hash, isDirty bool) {
	// Check if eviction is needed
	if sfc.totalCacheEntries >= sfc.maxCacheSize {
		sfc.evictLRUOptimized()
	}

	// Get or create cache entry
	entry := sfc.getPooledEntry()
	entry.Value = value
	entry.IsDirty = isDirty
	entry.ReadCount = 1
	entry.LastRead = time.Now()

	// Ensure contract cache exists
	if _, exists := sfc.cache[addr]; !exists {
		sfc.cache[addr] = make(map[common.Hash]*SfcCacheEntry)
	}
	sfc.cache[addr][slot] = entry

	// Add to LRU structures
	key := cacheKey{addr: addr, slot: slot}
	lruEntry := &lruEntry{key: key, entry: entry}
	element := sfc.lruList.PushFront(lruEntry)
	sfc.lruMap[key] = element
	sfc.totalCacheEntries++
}

// getPooledEntry gets a cache entry from the pool or creates a new one
func (sfc *SfcStateDB) getPooledEntry() *SfcCacheEntry {
	entry := sfc.entryPool.Get().(*SfcCacheEntry)
	// Reset entry fields
	entry.Value = common.Hash{}
	entry.IsDirty = false
	entry.ReadCount = 0
	entry.LastRead = time.Time{}
	return entry
}

// evictLRUOptimized removes the least recently used entry with O(1) complexity
func (sfc *SfcStateDB) evictLRUOptimized() {
	if sfc.lruList.Len() == 0 {
		return
	}

	// Get least recently used entry (back of list)
	element := sfc.lruList.Back()
	if element == nil {
		return
	}

	lruEntry := element.Value.(*lruEntry)
	key := lruEntry.key
	entry := lruEntry.entry

	// Flush if dirty before evicting
	if entry.IsDirty {
		sfc.StateDB.SetState(key.addr, key.slot, entry.Value)
		log.Debug("SFC cache eviction flush", "addr", key.addr.Hex(), "slot", key.slot.Hex())
	}

	// Remove from all data structures
	delete(sfc.cache[key.addr], key.slot)
	if len(sfc.cache[key.addr]) == 0 {
		delete(sfc.cache, key.addr)
	}
	delete(sfc.lruMap, key)
	sfc.lruList.Remove(element)
	sfc.totalCacheEntries--

	// Return entry to pool
	sfc.entryPool.Put(entry)

	log.Debug("SFC cache eviction", "addr", key.addr.Hex(), "slot", key.slot.Hex())
}

// evictLRUEntry removes the least recently used entry
func (sfc *SfcStateDB) evictLRUEntry() {
	var oldestAddr common.Address
	var oldestSlot common.Hash
	var oldestTime time.Time
	var oldestEntry *SfcCacheEntry

	first := true
	for addr, contractCache := range sfc.cache {
		for slot, entry := range contractCache {
			if first || entry.LastRead.Before(oldestTime) {
				oldestAddr = addr
				oldestSlot = slot
				oldestTime = entry.LastRead
				oldestEntry = entry
				first = false
			}
		}
	}

	if oldestEntry != nil {
		// Flush if dirty before evicting
		if oldestEntry.IsDirty {
			sfc.StateDB.SetState(oldestAddr, oldestSlot, oldestEntry.Value)
			log.Debug("SFC cache eviction flush", "addr", oldestAddr.Hex(), "slot", oldestSlot.Hex())
		}

		// Remove from cache
		delete(sfc.cache[oldestAddr], oldestSlot)
		if len(sfc.cache[oldestAddr]) == 0 {
			delete(sfc.cache, oldestAddr)
		}

		log.Debug("SFC cache eviction", "addr", oldestAddr.Hex(), "slot", oldestSlot.Hex())
	}
}

// flushDirtyEntries writes all dirty cache entries to StateDB
func (sfc *SfcStateDB) flushDirtyEntries() {
	dirtyCount := 0
	contractStats := make(map[common.Address]struct {
		entries int
		dirty   int
		reads   int
	})

	// Write all dirty entries to StateDB and collect statistics
	for addr, contractCache := range sfc.cache {
		stats := contractStats[addr]
		stats.entries = len(contractCache)

		for slot, entry := range contractCache {
			stats.reads += int(entry.ReadCount)

			if entry.IsDirty {
				sfc.StateDB.SetState(addr, slot, entry.Value)
				entry.IsDirty = false
				dirtyCount++
				stats.dirty++

				log.Debug("SFC cache flush", "addr", addr.Hex(), "slot", slot.Hex(), "value", entry.Value.Hex())
			}
		}
		contractStats[addr] = stats
	}

	// Calculate comprehensive statistics
	totalReads := sfc.readHits + sfc.readMisses
	totalWrites := sfc.writeHits + sfc.writeMisses
	totalOperations := totalReads + totalWrites

	var readHitRate, writeHitRate, overallHitRate float64
	if totalReads > 0 {
		readHitRate = float64(sfc.readHits) / float64(totalReads) * 100
	}
	if totalWrites > 0 {
		writeHitRate = float64(sfc.writeHits) / float64(totalWrites) * 100
	}
	if totalOperations > 0 {
		overallHitRate = float64(sfc.readHits+sfc.writeHits) / float64(totalOperations) * 100
	}

	// Log comprehensive epoch cache statistics
	log.Info("=== SFC CACHE STATISTICS ===",
		"epoch", sfc.currentEpoch,
		"totalOperations", totalOperations,
		"cacheEntries", sfc.totalCacheEntries,
		"dirtyEntries", dirtyCount)

	log.Info("Read Operations",
		"readHits", sfc.readHits,
		"readMisses", sfc.readMisses,
		"readHitRate", readHitRate)

	log.Info("Write Operations",
		"writeHits", sfc.writeHits,
		"writeMisses", sfc.writeMisses,
		"writeHitRate", writeHitRate)

	log.Info("Overall Performance",
		"overallHitRate", overallHitRate,
		"memoryEfficiency", float64(sfc.totalCacheEntries)/float64(totalOperations)*100)

	// Log per-contract statistics
	for addr, stats := range contractStats {
		if stats.entries > 0 {
			log.Info("Contract Cache Stats",
				"contract", addr.Hex(),
				"cacheEntries", stats.entries,
				"dirtyEntries", stats.dirty,
				"totalReads", stats.reads,
				"avgReadsPerEntry", float64(stats.reads)/float64(stats.entries))
		}
	}

	log.Info("SFC cache flushed to StateDB - Epoch Complete")
}

// checkPeriodicLogging logs cache statistics periodically during operations
func (sfc *SfcStateDB) checkPeriodicLogging() {
	readHits := atomic.LoadUint64(&sfc.readHits)
	readMisses := atomic.LoadUint64(&sfc.readMisses)
	writeHits := atomic.LoadUint64(&sfc.writeHits)
	writeMisses := atomic.LoadUint64(&sfc.writeMisses)
	totalOps := readHits + readMisses + writeHits + writeMisses

	if totalOps > 0 && totalOps%sfc.logInterval == 0 && totalOps != sfc.lastLoggedOps {
		sfc.lastLoggedOps = totalOps

		// Calculate current hit rates
		totalReads := readHits + readMisses
		totalWrites := writeHits + writeMisses

		var readHitRate, writeHitRate, overallHitRate float64
		if totalReads > 0 {
			readHitRate = float64(readHits) / float64(totalReads) * 100
		}
		if totalWrites > 0 {
			writeHitRate = float64(writeHits) / float64(totalWrites) * 100
		}
		if totalOps > 0 {
			overallHitRate = float64(readHits+writeHits) / float64(totalOps) * 100
		}

		log.Info("SFC Cache Progress Update",
			"epoch", sfc.currentEpoch,
			"totalOps", totalOps,
			"cacheEntries", sfc.totalCacheEntries,
			"readHitRate", readHitRate,
			"writeHitRate", writeHitRate,
			"overallHitRate", overallHitRate)
	}
}

// logFinalStatistics logs comprehensive final statistics
func (sfc *SfcStateDB) logFinalStatistics() {
	readHits := atomic.LoadUint64(&sfc.readHits)
	readMisses := atomic.LoadUint64(&sfc.readMisses)
	writeHits := atomic.LoadUint64(&sfc.writeHits)
	writeMisses := atomic.LoadUint64(&sfc.writeMisses)

	totalReads := readHits + readMisses
	totalWrites := writeHits + writeMisses
	totalOperations := totalReads + totalWrites

	var readHitRate, writeHitRate, overallHitRate float64
	if totalReads > 0 {
		readHitRate = float64(readHits) / float64(totalReads) * 100
	}
	if totalWrites > 0 {
		writeHitRate = float64(writeHits) / float64(totalWrites) * 100
	}
	if totalOperations > 0 {
		overallHitRate = float64(readHits+writeHits) / float64(totalOperations) * 100
	}

	// Calculate estimated gas savings
	readGasSaved := readHits * 2100    // SLOAD gas cost
	writeGasSaved := writeHits * 20000 // SSTORE gas cost
	totalGasSaved := readGasSaved + writeGasSaved

	// Log comprehensive final statistics
	log.Info("=== SFC CACHE FINAL SUMMARY ===",
		"epoch", sfc.currentEpoch,
		"totalOperations", totalOperations,
		"overallHitRate", overallHitRate)

	log.Info("Cache Performance Breakdown",
		"readHits", readHits,
		"readMisses", readMisses,
		"readHitRate", readHitRate,
		"writeHits", writeHits,
		"writeMisses", writeMisses,
		"writeHitRate", writeHitRate)

	log.Info("Gas Savings Estimate",
		"readGasSaved", readGasSaved,
		"writeGasSaved", writeGasSaved,
		"totalGasSaved", totalGasSaved,
		"avgGasSavedPerOp", float64(totalGasSaved)/float64(totalOperations))
}

// GetCacheStats returns current cache statistics
func (sfc *SfcStateDB) GetCacheStats() (readHits, readMisses, writeHits, writeMisses uint64, hitRate float64) {
	sfc.mutex.RLock()
	defer sfc.mutex.RUnlock()

	totalOps := sfc.readHits + sfc.readMisses + sfc.writeHits + sfc.writeMisses
	if totalOps > 0 {
		hitRate = float64(sfc.readHits+sfc.writeHits) / float64(totalOps) * 100
	}

	return sfc.readHits, sfc.readMisses, sfc.writeHits, sfc.writeMisses, hitRate
}

// IsCacheEnabled returns whether caching is currently enabled
func (sfc *SfcStateDB) IsCacheEnabled() bool {
	sfc.mutex.RLock()
	defer sfc.mutex.RUnlock()
	return sfc.cacheEnabled
}

// SetCacheSize sets the maximum cache size
func (sfc *SfcStateDB) SetCacheSize(size int) {
	sfc.mutex.Lock()
	defer sfc.mutex.Unlock()
	sfc.maxCacheSize = size
}

// Global SfcStateDB cache manager
var (
	globalSfcCache *SfcStateDB
	cacheMutex     sync.RWMutex
)

// GetOrCreateSfcStateDB returns the global SfcStateDB instance or creates one
func GetOrCreateSfcStateDB(stateDB *state.StateDB) *SfcStateDB {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	if globalSfcCache == nil || globalSfcCache.StateDB != stateDB {
		globalSfcCache = NewSfcStateDB(stateDB)
	}

	return globalSfcCache
}

// EnableSfcCacheForEpoch enables caching for the given epoch
func EnableSfcCacheForEpoch(stateDB *state.StateDB, epoch uint64) *SfcStateDB {
	sfcStateDB := GetOrCreateSfcStateDB(stateDB)
	sfcStateDB.EnableCache(epoch)
	return sfcStateDB
}

// DisableSfcCache disables caching and flushes all dirty entries
func DisableSfcCache() {
	cacheMutex.RLock()
	defer cacheMutex.RUnlock()

	if globalSfcCache != nil {
		globalSfcCache.DisableCache()
	}
}

// WithSfcCache executes a function with SFC caching enabled
func WithSfcCache(stateDB *state.StateDB, epoch uint64, fn func(*SfcStateDB) error) error {
	sfcStateDB := EnableSfcCacheForEpoch(stateDB, epoch)
	defer sfcStateDB.DisableCache()

	return fn(sfcStateDB)
}

// Helper functions for easy integration

// StartEpochCaching starts caching for epoch sealing operations
func StartEpochCaching(stateDB *state.StateDB, epoch uint64) *SfcStateDB {
	return EnableSfcCacheForEpoch(stateDB, epoch)
}

// StopEpochCaching stops caching and flushes all changes
func StopEpochCaching() {
	DisableSfcCache()
}
